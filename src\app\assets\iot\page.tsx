export default function IoTDevicesPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">IoT Devices</h1>
        <p className="text-muted-foreground">
          Connected sensors, smart devices, edge computing
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Sensors</h3>
          <p className="text-2xl font-bold text-green-600">2,456</p>
          <p className="text-sm text-muted-foreground">Environmental & security</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Smart Devices</h3>
          <p className="text-2xl font-bold text-blue-600">789</p>
          <p className="text-sm text-muted-foreground">Connected appliances</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Edge Computing</h3>
          <p className="text-2xl font-bold text-purple-600">123</p>
          <p className="text-sm text-muted-foreground">Edge nodes</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Gateways</h3>
          <p className="text-2xl font-bold text-orange-600">88</p>
          <p className="text-sm text-muted-foreground">IoT gateways</p>
        </div>
      </div>
    </div>
  )
}
