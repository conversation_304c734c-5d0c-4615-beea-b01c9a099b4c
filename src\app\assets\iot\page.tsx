"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Network, 
  Plus,
  Filter,
  Search,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Shield,
  Wifi,
  Battery,
  Edit,
  Archive
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

import { sampleIoTAssets } from "@/lib/assets-data"
import { IoTAsset } from "@/types/assets"

const criticalityConfig = {
  critical: { label: "Critical", variant: "destructive" as const },
  high: { label: "High", variant: "destructive" as const },
  medium: { label: "Medium", variant: "secondary" as const },
  low: { label: "Low", variant: "outline" as const }
}

const securityStatusConfig = {
  compliant: { label: "Compliant", variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
  "non-compliant": { label: "Non-Compliant", variant: "destructive" as const, icon: XCircle, color: "text-red-600" },
  unknown: { label: "Unknown", variant: "secondary" as const, icon: AlertTriangle, color: "text-gray-600" },
  pending: { label: "Pending", variant: "outline" as const, icon: Clock, color: "text-blue-600" }
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return "Just now"
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

function getBatteryColor(level: number): string {
  if (level >= 70) return "text-green-600"
  if (level >= 30) return "text-yellow-600"
  return "text-red-600"
}

function getSignalColor(strength: number): string {
  if (strength >= -50) return "text-green-600"
  if (strength >= -70) return "text-yellow-600"
  return "text-red-600"
}

export default function IoTDevicesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [assets] = useState<IoTAsset[]>(sampleIoTAssets)

  const filteredAssets = assets.filter(asset =>
    asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.assetType.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.location?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">IoT Devices</h1>
          <p className="text-muted-foreground">
            Connected sensors, smart devices, edge computing with connectivity and security status
          </p>
        </div>
        <Button asChild>
          <Link href="/assets/register?category=iot">
            <Plus className="mr-2 h-4 w-4" />
            Add IoT Device
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assets.length}</div>
            <p className="text-xs text-muted-foreground">
              Connected devices
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
            <Wifi className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.status === "active").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently connected
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Encrypted</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.connectivity?.encryptionEnabled).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Secure connections
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Battery</CardTitle>
            <Battery className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.device?.batteryLevel && a.device.batteryLevel < 30).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search IoT devices..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline" size="sm">
          <Network className="mr-2 h-4 w-4" />
          Network Map
        </Button>
      </div>

      {/* Devices Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Device Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Connectivity</TableHead>
              <TableHead>Battery</TableHead>
              <TableHead>Last Communication</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Security</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAssets.map((asset) => {
              const criticalityInfo = criticalityConfig[asset.criticalityLevel]
              const statusInfo = securityStatusConfig[asset.securityStatus]
              const StatusIcon = statusInfo.icon
              
              return (
                <TableRow key={asset.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Network className="h-4 w-4 text-green-600" />
                      <div className="space-y-1">
                        <div className="font-medium">{asset.name}</div>
                        {asset.device?.deviceId && (
                          <div className="text-xs text-muted-foreground">
                            ID: {asset.device.deviceId}
                          </div>
                        )}
                        {asset.device?.manufacturer && asset.device?.model && (
                          <div className="text-xs text-muted-foreground">
                            {asset.device.manufacturer} {asset.device.model}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {asset.assetType.replace("-", " ")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${asset.status === "active" ? "bg-green-500" : "bg-gray-400"}`}></div>
                      <span className="text-sm capitalize">{asset.status}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-1">
                        <Wifi className="h-3 w-3" />
                        <span className="text-sm">{asset.connectivity?.connectionType}</span>
                      </div>
                      {asset.connectivity?.signalStrength && (
                        <div className={`text-xs ${getSignalColor(asset.connectivity.signalStrength)}`}>
                          {asset.connectivity.signalStrength} dBm
                        </div>
                      )}
                      {asset.connectivity?.encryptionEnabled && (
                        <Badge variant="default" className="text-xs">
                          Encrypted
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {asset.device?.batteryLevel !== undefined ? (
                      <div className="space-y-1">
                        <div className={`text-sm font-medium ${getBatteryColor(asset.device.batteryLevel)}`}>
                          {asset.device.batteryLevel}%
                        </div>
                        <Progress value={asset.device.batteryLevel} className="h-1" />
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">N/A</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {asset.device?.lastCommunication ? 
                        formatRelativeTime(asset.device.lastCommunication) : 
                        formatRelativeTime(asset.lastSeen)
                      }
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{asset.location || "Unknown"}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
                      <Badge variant={statusInfo.variant} className="text-xs">
                        {statusInfo.label}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Network className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Wifi className="mr-2 h-4 w-4" />
                          Test Connection
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Shield className="mr-2 h-4 w-4" />
                          Security Scan
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Archive className="mr-2 h-4 w-4" />
                          Archive
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
}
