import {
  Activity,
  Archive,
  BarChart3,
  FileText,
  LayoutDashboard,
  Monitor,
  Package,
  Shield,
  ShieldCheck,
  Users,
  Workflow,
  Wrench,
  type LucideIcon,
} from "lucide-react"

export interface SubModule {
  title: string
  url: string
  description?: string
}

export interface NavigationItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  subModules?: SubModule[]
}

export interface NavigationData {
  user: {
    name: string
    email: string
    avatar: string
  }
  navMain: NavigationItem[]
}

// GRCOS navigation data - Flat, Agent-Orchestrated Architecture
export const navigationData: NavigationData = {
  user: {
    name: "GRC Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  // Entity management is now handled by EntitySwitcher component
  // Teams section replaced with multi-entity functionality
  navMain: [
    {
      title: "Overview",
      url: "/overview/executive",
      icon: LayoutDashboard,
      subModules: [
        { title: "Executive Summary", url: "/overview/executive", description: "High-level executive dashboard view" },
        { title: "System Agent Status", url: "/overview/agents", description: "Real-time agent activity and orchestration health" },
        { title: "Risk Posture", url: "/overview/risk", description: "Cross-domain risk posture overview" },
        { title: "Compliance Status", url: "/overview/compliance", description: "Overall compliance status across frameworks" },
      ]
    },
    {
      title: "Activity",
      url: "/activity/events",
      icon: Activity,
      subModules: [
        { title: "Event Stream", url: "/activity/events", description: "Unified event stream from all system components" },
        { title: "Agent Actions", url: "/activity/agents", description: "Agent-initiated actions and automated responses" },
        { title: "Security Events", url: "/activity/security", description: "Security events and compliance activities" },
        { title: "Audit Trail", url: "/activity/audit", description: "Audit trail with blockchain verification links" },
      ]
    },
    {
      title: "Assets",
      url: "/assets/it",
      icon: Package,
      subModules: [
        { title: "IT Assets", url: "/assets/it", description: "Servers, workstations, network infrastructure with hardware specifications and software inventory" },
        { title: "OT Assets", url: "/assets/ot", description: "Industrial control systems, SCADA, manufacturing equipment with operational technology details" },
        { title: "IoT Devices", url: "/assets/iot", description: "Connected sensors, smart devices, edge computing with connectivity and security status" },
        { title: "Identities", url: "/assets/identities", description: "Users, service accounts, privileged access management with access rights and permissions" },
        { title: "Applications", url: "/assets/applications", description: "Software inventory, cloud services, SaaS platforms with security configurations and dependencies" },
        { title: "Vendors", url: "/assets/vendors", description: "Third-party relationships, supplier risk management with contract details and risk assessments" },
        { title: "Processes", url: "/assets/processes", description: "Business processes, workflows, procedures with asset dependencies and compliance requirements" },
      ]
    },
    {
      title: "Monitor",
      url: "/monitor/security",
      icon: Monitor,
      subModules: [
        { title: "Security Dashboard", url: "/monitor/security", description: "Real-time security monitoring across all asset categories" },
        { title: "Anomaly Detection", url: "/monitor/anomaly", description: "AI-powered anomaly detection and behavioral analysis" },
        { title: "SIEM Dashboard", url: "/monitor/siem", description: "Unified SIEM dashboard with Wazuh integration" },
        { title: "Threat Correlation", url: "/monitor/correlation", description: "Cross-environment threat correlation (IT/OT/IoT)" },
        { title: "Alert Prioritization", url: "/monitor/alerts", description: "System Agent coordinated alert prioritization" },
      ]
    },
    {
      title: "Frameworks",
      url: "/frameworks",
      icon: Shield,
      // No sub-modules - single unified frameworks interface
    },
    {
      title: "Controls",
      url: "/controls/implementation",
      icon: ShieldCheck,
      subModules: [
        { title: "Implementation", url: "/controls/implementation", description: "Security control implementation and tracking" },
        { title: "Testing", url: "/controls/testing", description: "OSCAL standardized control testing" },
        { title: "Assessment", url: "/controls/assessment", description: "Compliance Agent automated control assessment" },
        { title: "Validation", url: "/controls/validation", description: "Control validation and effectiveness measurement" },
        { title: "Mapping", url: "/controls/mapping", description: "Cross-framework control mapping" },
      ]
    },
    {
      title: "Policies",
      url: "/policies/management",
      icon: FileText,
      subModules: [
        { title: "Management", url: "/policies/management", description: "Policy management and enforcement across all environments" },
        { title: "Policy as Code", url: "/policies/code", description: "OPA-based policy-as-code implementation" },
        { title: "Translation", url: "/policies/translation", description: "Compliance Agent policy translation and application" },
        { title: "Consistency", url: "/policies/consistency", description: "Cross-environment policy consistency verification" },
        { title: "Enforcement", url: "/policies/enforcement", description: "OPA policy enforcement integration" },
      ]
    },
    {
      title: "Assessments",
      url: "/assessments/risk",
      icon: BarChart3,
      subModules: [
        { title: "Risk Assessments", url: "/assessments/risk", description: "Automated risk assessments and gap analysis" },
        { title: "Control Testing", url: "/assessments/testing", description: "Assessment Agent orchestrated control testing" },
        { title: "Gap Analysis", url: "/assessments/gaps", description: "Compliance gap identification and analysis" },
        { title: "Quantitative Risk", url: "/assessments/quantitative", description: "Quantitative risk analysis with Open Source Risk Engine" },
        { title: "Continuous Assessment", url: "/assessments/continuous", description: "Continuous assessment scheduling and execution" },
      ]
    },
    {
      title: "Workflows",
      url: "/workflows/automation",
      icon: Workflow,
      subModules: [
        { title: "Process Automation", url: "/workflows/automation", description: "Business process automation with Flowable engine" },
        { title: "Optimization", url: "/workflows/optimization", description: "Workflow Agent orchestrated process optimization" },
        { title: "Design", url: "/workflows/design", description: "Custom workflow design and template management" },
        { title: "Integration", url: "/workflows/integration", description: "Integration with compliance and security processes" },
        { title: "Analytics", url: "/workflows/analytics", description: "Workflow performance analytics" },
      ]
    },
    {
      title: "Remediation",
      url: "/remediation/incidents",
      icon: Wrench,
      subModules: [
        { title: "Incident Response", url: "/remediation/incidents", description: "Incident response and security remediation coordination" },
        { title: "Automated Response", url: "/remediation/automated", description: "Remediation Agent automated response orchestration" },
        { title: "DFIR Integration", url: "/remediation/dfir", description: "DFIR-IRIS integration for structured investigation" },
        { title: "Cross-Environment", url: "/remediation/cross-env", description: "Cross-environment remediation tracking and validation" },
        { title: "Validation", url: "/remediation/validation", description: "Remediation validation and testing" },
      ]
    },
    {
      title: "Reports",
      url: "/reports",
      icon: FileText,
      // No sub-modules - single unified reports interface
    },
    {
      title: "Artifacts",
      url: "/artifacts",
      icon: Archive,
      // No sub-modules - single unified artifacts interface
    },
    {
      title: "Portals",
      url: "/portals",
      icon: Users,
      // No sub-modules - portal dashboard is the primary interface
    },
  ],
}
