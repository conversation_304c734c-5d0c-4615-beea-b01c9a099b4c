"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { navigationData } from "@/lib/navigation-data"

export function SubNavigation() {
  const pathname = usePathname()
  const router = useRouter()

  // Find the current active main item and its sub-modules
  const { activeMainItem, subModules } = React.useMemo(() => {
    for (const mainItem of navigationData.navMain) {
      if (pathname === mainItem.url || pathname.startsWith(mainItem.url + "/")) {
        if (mainItem.subModules && mainItem.subModules.length > 0) {
          return { activeMainItem: mainItem, subModules: mainItem.subModules }
        }
      }
    }
    return { activeMainItem: null, subModules: null }
  }, [pathname])

  // No auto-redirect needed since navigation URLs now point directly to first sub-module

  // Don't render if no active main item or no sub-modules
  if (!activeMainItem || !subModules || subModules.length === 0) {
    return null
  }

  return (
    <nav className="flex items-center gap-2">
      {subModules.map((subModule) => {
        const isActive = pathname === subModule.url

        return (
          <Button
            key={subModule.url}
            variant="ghost"
            size="sm"
            asChild
            className={cn(
              "h-8 px-4 text-sm font-medium transition-all duration-200",
              "hover:bg-gray-50 dark:hover:bg-gray-800/30",
              "text-gray-600 dark:text-gray-400",
              isActive && [
                "bg-gray-100 text-gray-900 hover:bg-gray-150",
                "dark:bg-gray-800/60 dark:text-gray-100 dark:hover:bg-gray-800/80",
                "border-b-2 border-gray-400 dark:border-gray-500 rounded-b-none"
              ]
            )}
          >
            <Link href={subModule.url}>
              {subModule.title}
            </Link>
          </Button>
        )
      })}
    </nav>
  )
}
