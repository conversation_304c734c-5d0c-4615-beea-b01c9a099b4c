"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Package, 
  Plus,
  Filter,
  Search,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Shield,
  Globe,
  Edit,
  Archive
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { sampleApplicationAssets } from "@/lib/assets-data"
import { ApplicationAsset } from "@/types/assets"

export default function ApplicationsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [assets] = useState<ApplicationAsset[]>(sampleApplicationAssets)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Applications</h1>
          <p className="text-muted-foreground">
            Software inventory, cloud services, SaaS platforms with security configurations and dependencies
          </p>
        </div>
        <Button asChild>
          <Link href="/assets/register?category=application">
            <Plus className="mr-2 h-4 w-4" />
            Add Application
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assets.length}</div>
            <p className="text-xs text-muted-foreground">
              Managed applications
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cloud Services</CardTitle>
            <Globe className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.application?.deploymentType === "cloud").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Cloud-based apps
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Availability</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.performance && a.performance.availability >= 99).length}
            </div>
            <p className="text-xs text-muted-foreground">
              99%+ uptime
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Vulnerabilities</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.reduce((sum, a) => sum + (a.security?.vulnerabilities.length || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total findings
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search applications..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline" size="sm">
          <Shield className="mr-2 h-4 w-4" />
          Security Scan
        </Button>
      </div>

      {/* Applications Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Application Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Version</TableHead>
              <TableHead>Deployment</TableHead>
              <TableHead>Security Rating</TableHead>
              <TableHead>Availability</TableHead>
              <TableHead>Vulnerabilities</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assets.map((asset) => (
              <TableRow key={asset.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Package className="h-4 w-4 text-indigo-600" />
                    <div className="space-y-1">
                      <div className="font-medium">{asset.name}</div>
                      {asset.application?.url && (
                        <div className="text-xs text-muted-foreground">
                          {asset.application.url}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="capitalize">
                    {asset.assetType.replace("-", " ")}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{asset.application?.version || "Unknown"}</div>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary" className="capitalize">
                    {asset.application?.deploymentType || "Unknown"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="default">
                    {asset.security?.securityRating || "Unrated"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {asset.performance?.availability ? `${asset.performance.availability}%` : "Unknown"}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {asset.security?.vulnerabilities.length || 0}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{asset.owner}</div>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Package className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Shield className="mr-2 h-4 w-4" />
                        Security Scan
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
}
