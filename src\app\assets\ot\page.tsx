"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Workflow, 
  Plus,
  Filter,
  Search,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Shield,
  Scan,
  Edit,
  Archive,
  Settings
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

import { sampleOTAssets } from "@/lib/assets-data"
import { OTAsset } from "@/types/assets"

const criticalityConfig = {
  critical: { label: "Critical", variant: "destructive" as const, color: "text-red-600 dark:text-red-400" },
  high: { label: "High", variant: "destructive" as const, color: "text-orange-600 dark:text-orange-400" },
  medium: { label: "Medium", variant: "secondary" as const, color: "text-yellow-600 dark:text-yellow-400" },
  low: { label: "Low", variant: "outline" as const, color: "text-green-600 dark:text-green-400" }
}

const securityStatusConfig = {
  compliant: { 
    label: "Compliant", 
    variant: "default" as const, 
    icon: CheckCircle, 
    color: "text-green-600 dark:text-green-400" 
  },
  "non-compliant": { 
    label: "Non-Compliant", 
    variant: "destructive" as const, 
    icon: XCircle, 
    color: "text-red-600 dark:text-red-400" 
  },
  unknown: { 
    label: "Unknown", 
    variant: "secondary" as const, 
    icon: AlertTriangle, 
    color: "text-gray-600 dark:text-gray-400" 
  },
  pending: { 
    label: "Pending", 
    variant: "outline" as const, 
    icon: Clock, 
    color: "text-blue-600 dark:text-blue-400" 
  }
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return "Just now"
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

function getRiskColor(score: number): string {
  if (score >= 70) return "text-red-600 dark:text-red-400"
  if (score >= 50) return "text-orange-600 dark:text-orange-400"
  if (score >= 30) return "text-yellow-600 dark:text-yellow-400"
  return "text-green-600 dark:text-green-400"
}

export default function OTAssetsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [assets] = useState<OTAsset[]>(sampleOTAssets)

  const filteredAssets = assets.filter(asset =>
    asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.assetType.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.owner.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">OT Assets</h1>
          <p className="text-muted-foreground">
            Industrial control systems, SCADA, manufacturing equipment with operational technology details
          </p>
        </div>
        <Button asChild>
          <Link href="/assets/register?category=ot">
            <Plus className="mr-2 h-4 w-4" />
            Add OT Asset
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total OT Assets</CardTitle>
            <Workflow className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assets.length}</div>
            <p className="text-xs text-muted-foreground">
              Industrial systems
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Assets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.criticalityLevel === "critical").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Production critical
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Non-Compliant</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.securityStatus === "non-compliant").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Risk Score</CardTitle>
            <Shield className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.length > 0 ? Math.round(assets.reduce((sum, a) => sum + a.riskScore, 0) / assets.length) : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 100
            </p>
          </CardContent>
        </Card>
      </div>

      {/* OT Security Alert */}
      <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
            <AlertTriangle className="h-5 w-5" />
            OT Security Notice
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-orange-700 dark:text-orange-300">
            Several OT assets are running legacy firmware without encryption. Consider implementing network segmentation 
            and updating to secure protocols. Review IEC 62443 compliance requirements.
          </p>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search OT assets..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          Configure Discovery
        </Button>
      </div>

      {/* Assets Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Criticality</TableHead>
              <TableHead>Security Status</TableHead>
              <TableHead>Protocol</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Risk Score</TableHead>
              <TableHead>Production Line</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAssets.map((asset) => {
              const criticalityInfo = criticalityConfig[asset.criticalityLevel]
              const statusInfo = securityStatusConfig[asset.securityStatus]
              const StatusIcon = statusInfo.icon
              
              return (
                <TableRow key={asset.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Workflow className="h-4 w-4 text-orange-600" />
                      <div className="space-y-1">
                        <div className="font-medium">{asset.name}</div>
                        {asset.description && (
                          <div className="text-sm text-muted-foreground line-clamp-1">
                            {asset.description}
                          </div>
                        )}
                        {asset.industrial?.vendor && asset.industrial?.model && (
                          <div className="text-xs text-muted-foreground">
                            {asset.industrial.vendor} {asset.industrial.model}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {asset.assetType.replace("-", " ")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={criticalityInfo.variant}>
                      {criticalityInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
                      <Badge variant={statusInfo.variant}>
                        {statusInfo.label}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {asset.industrial?.protocol || "Unknown"}
                      </div>
                      {asset.connectivity?.encryptionEnabled === false && (
                        <Badge variant="destructive" className="text-xs">
                          No Encryption
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {formatRelativeTime(asset.lastSeen)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {asset.discoveryMethod}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className={`text-sm font-medium ${getRiskColor(asset.riskScore)}`}>
                        {asset.riskScore}
                      </div>
                      <Progress value={asset.riskScore} className="h-1" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {asset.operational?.productionLine || "N/A"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {asset.operational?.facility}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Workflow className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="mr-2 h-4 w-4" />
                          Configure
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Shield className="mr-2 h-4 w-4" />
                          Security Assessment
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Archive className="mr-2 h-4 w-4" />
                          Archive
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
}
