export default function OTAssetsPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">OT Assets</h1>
        <p className="text-muted-foreground">
          Industrial control systems, SCADA, manufacturing equipment
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">SCADA Systems</h3>
          <p className="text-2xl font-bold text-green-600">45</p>
          <p className="text-sm text-muted-foreground">Operational systems</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">PLCs</h3>
          <p className="text-2xl font-bold text-blue-600">234</p>
          <p className="text-sm text-muted-foreground">Programmable Logic Controllers</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">HMIs</h3>
          <p className="text-2xl font-bold text-purple-600">67</p>
          <p className="text-sm text-muted-foreground">Human Machine Interfaces</p>
        </div>
      </div>
    </div>
  )
}
